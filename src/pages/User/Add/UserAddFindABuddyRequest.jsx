import React, { useState, useEffect, useContext, useMemo } from "react";
import BottomDrawer from "Components/Drawers/BottomDrawer";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import SelectionOption from "Components/SelectionOption";
import { format } from "date-fns";
import { BackButton } from "Components/BackButton";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import { Link } from "react-router-dom";
import TreeSDK from "Utils/TreeSDK";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "Components/LoadingSpinner";
import {
  ntrpOptions,
  updateBrowserTab,
  formatTimeToSQL,
  calculateServiceFee,
  getTimeRangeFromSlots,
  activityLogTypes,
  actionLogTypes,
} from "Utils/utils";
import TimeSlots from "Components/TimeSlots/TimeSlots";
import AddPlayers from "Components/Players/AddPlayers";
import SportTypeSelection from "Components/Shared/SportTypeSelection";
import { Calendar } from "Components/Calendar";
import {
  BuddyRequestReservationSummary,
  CourtReservationSummary,
} from "Components/Reservation/ReservationSummary";
import { useClub } from "Context/Club";
import WarningModal from "Components/Modals/WarningModal";
import Select from "react-select";

let sdk = new MkdSDK();
let tdk = new TreeSDK();

const UserAddFindABuddyRequest = ({}) => {
  const [selectedSport, setSelectedSport] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedSurface, setSelectedSurface] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState(null);
  const [players, setPlayers] = useState([]);
  const [groups, setGroups] = useState([]);
  const [club, setClub] = useState(null);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedSubType, setSelectedSubType] = useState(null);
  const [fee, setFee] = useState(0.0);
  const [totalFee, setTotalFee] = useState(0.0);
  const [selectedTimes, setSelectedTimes] = useState([]);
  const [sports, setSports] = useState([]);

  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timeRange, setTimeRange] = useState({ from: null, until: null });
  const [timeSlots, setTimeSlots] = useState([{ from: null, until: null }]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  const [isFindBuddyEnabled, setIsFindBuddyEnabled] = useState(false);
  const [playersNeeded, setPlayersNeeded] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [notes, setNotes] = useState("");
  const [playersPlaying, setPlayersPlaying] = useState(1);
  const [ntrpMin, setNtrpMin] = useState(3.5);
  const [ntrpMax, setNtrpMax] = useState(3.5);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [searchLoading, setSearchLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [primaryPlayer, setPrimaryPlayer] = useState(null);

  const { user_subscription, user_permissions, club_membership } = useClub();

  // Get the user's membership plan
  const userMembershipPlan = useMemo(() => {
    if (!user_subscription?.planId || !club_membership?.length) return null;
    return club_membership.find(
      (plan) => plan.plan_id === user_subscription.planId
    );
  }, [user_subscription, club_membership]);

  // Calculate the maximum allowed date based on the user's membership plan
  const maxAllowedDate = useMemo(() => {
    // Check if advance booking is enabled for buddy requests
    if (userMembershipPlan?.advance_booking_enabled?.buddy === false) {
      // If advance booking is disabled, allow booking without limit
      const farFutureDate = new Date();
      farFutureDate.setFullYear(farFutureDate.getFullYear() + 10); // Set to 10 years in the future
      return farFutureDate;
    }

    // Default to 10 days in advance if no plan or advance booking days are specified
    const advanceDays = userMembershipPlan?.advance_booking_days?.buddy || 10;
    const today = new Date();
    const maxDate = new Date();
    maxDate.setDate(today.getDate() + advanceDays);
    return maxDate;
  }, [userMembershipPlan]);

  const [warningModal, setWarningModal] = useState({
    isOpen: false,
    title: "",
    message: "",
    actionButtonText: "",
    actionButtonLink: "",
    type: "warning",
  });

  const navigate = useNavigate();

  const user_id = localStorage.getItem("user");

  const fetchSports = async () => {
    try {
      const userResponse = await tdk.getOne("user", user_id, {});
      const clubResponse2 = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/club/${userResponse.model.club_id}`,
        {},
        "GET"
      );

      setSports(clubResponse2.sports);
      setClub(clubResponse2.model);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchPlayers = async () => {
    try {
      const playersResponse = await tdk.getList("user", {
        filter: [`role,cs,user`],
      });

      setPlayers(playersResponse.list);
    } catch (error) {
      console.error(error);
    }
  };
  const fetchGroups = async () => {
    try {
      const groupsResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/groups",
        {},
        "GET"
      );
      setGroups(groupsResponse.groups);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchFamilyMembers = async () => {
    try {
      const familyResponse = await tdk.getList("user", {
        filter: [`guardian,eq,${user_id}`, `role,cs,user`],
      });
      setFamilyMembers(familyResponse.list);
    } catch (error) {
      console.error("Error fetching family members:", error);
    }
  };

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      await fetchSports();
      await fetchPlayers();
      await fetchGroups();
      await fetchFamilyMembers();
      setIsLoading(false);
    })();
  }, []);

  React.useEffect(() => {
    updateBrowserTab({
      path: "/user/create-request",
      clubName: club?.name,
      favicon: club?.club_logo,
      description: "Create Request",
    });
  }, [club?.club_logo]);

  const handlePreviousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.setMonth(currentMonth.getMonth() - 1))
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.setMonth(currentMonth.getMonth() + 1))
    );
  };

  const handleSave = async () => {
    if (!user_subscription?.planId) {
      setWarningModal({
        isOpen: true,
        title: "Subscription Required",
        message:
          "Please subscribe to a membership plan to use the Find a Buddy feature",
        actionButtonText: "View Membership Plans",
        actionButtonLink: "/user/membership/buy",
        type: "warning",
      });
      return;
    }

    if (!user_permissions?.allowBuddy) {
      setWarningModal({
        isOpen: true,
        title: "Plan Upgrade Required",
        message: `Your current plan (${user_permissions?.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,
        actionButtonText: "Upgrade Plan",
        actionButtonLink: "/user/membership/buy",
        type: "error",
      });
      return;
    }

    // Check if the selected date is within the allowed range
    if (
      selectedDate > maxAllowedDate &&
      userMembershipPlan?.advance_booking_enabled?.buddy !== false
    ) {
      const advanceDays = userMembershipPlan?.advance_booking_days?.buddy || 10;
      setWarningModal({
        isOpen: true,
        title: "Date Selection Error",
        message: `Your membership plan only allows creating buddy requests ${advanceDays} days in advance. Please select a valid date.`,
        type: "warning",
      });
      return;
    }

    if (
      !selectedSport ||
      !selectedDate ||
      !timeSlots.length ||
      !selectedType ||
      !selectedSubType
    ) {
      setWarningModal({
        isOpen: true,
        title: "Incomplete Details",
        message: "Please fill in all required fields",
        type: "warning",
      });
      return;
    }

    const { start_time, end_time } = getTimeRangeFromSlots(timeSlots);
    setIsSubmitting(true);

    try {
      const dateTime = new Date(selectedDate);
      const validTimeSlots = timeSlots
        .filter((slot) => slot.from && slot.until)
        .map((slot) => ({
          start_time: formatTimeToSQL(slot.from),
          end_time: formatTimeToSQL(slot.until),
        }));

      if (validTimeSlots.length === 0) {
        setWarningModal({
          isOpen: true,
          title: "Time Slots Required",
          message: "Please select at least one valid time slot",
          type: "warning",
        });
        return;
      }

      const payload = {
        sport_id: selectedSport,
        slots: validTimeSlots,
        ntrp: ntrpMin,
        max_ntrp: ntrpMax,
        num_players: playersPlaying,
        num_needed: playersNeeded,
        type: selectedType,
        sub_type: selectedSubType,
        need_coach: 1,
        notes: notes,
        date: format(dateTime, "yyyy-MM-dd"),
        start_time: start_time,
        end_time: end_time,
        player_ids: selectedPlayers.map((p) => p.id),
        primary_player_id: primaryPlayer?.id || parseInt(user_id), // Add primary player ID
      };

      const response = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/buddy/create-request",
        payload,
        "POST"
      );

      sdk.setTable("activity_logs");
      const activityResponse = await sdk.callRestAPI(
        {
          activity_type: activityLogTypes.find_a_buddy,
          user_id: user_id,
          club_id: club?.id,
          action_type: actionLogTypes.CREATE,
          data: JSON.stringify(payload),
          description: "Created a find a buddy request",
        },
        "POST"
      );
      if (!response.error) {
        showToast(
          globalDispatch,
          "Request created successfully",
          3000,
          "success"
        );
        navigate("/user/find-a-buddy");
      }
    } catch (error) {
      console.error(error);
      setWarningModal({
        isOpen: true,
        title: "Request Error",
        message: error.message || "Error creating buddy request",
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTimeClick = (newTimeRange) => {
    setTimeRange(newTimeRange);
    setSelectedTime(newTimeRange.from);
  };

  const handleTimeSlotsChange = (newTimeSlots) => {
    setTimeSlots(newTimeSlots);
  };

  useEffect(() => {
    if (fee && selectedPlayers?.length) {
      const baseAmount = fee * selectedPlayers?.length;
      const serviceFee = calculateServiceFee(club?.fee_settings, baseAmount);
      const clubFee = club?.club_fee || 0;
      setTotalFee(baseAmount + serviceFee + clubFee);
    } else {
      const serviceFee = calculateServiceFee(club?.fee_settings, fee);
      const clubFee = club?.club_fee || 0;
      setTotalFee(fee + serviceFee + clubFee);
    }
  }, [fee, selectedPlayers, club?.fee_settings, club?.club_fee]);

  const handleIncrement = () => {
    setPlayersNeeded((prev) => Math.min(prev + 1, 10)); // Maximum 10 players
  };

  const handleDecrement = () => {
    setPlayersNeeded((prev) => Math.max(prev - 1, 0)); // Minimum 0 players
  };

  const handleNextPlayers = () => {
    setCurrentStep(2);
  };

  const togglePlayer = (player) => {
    setSelectedPlayers((prev) => {
      const isSelected = prev.some((p) => p.id === player.id);
      if (isSelected) {
        return prev.filter((p) => p.id !== player.id);
      }
      return [...prev, player];
    });
  };

  const handlePrimaryPlayerChange = (selectedOption) => {
    // Handle both Select component (with .value) and direct user object
    const newPrimaryPlayer = selectedOption.value || selectedOption;

    // Don't do anything if the same player is selected
    if (newPrimaryPlayer?.id === primaryPlayer?.id) {
      return;
    }

    setPrimaryPlayer(newPrimaryPlayer);

    // Update selected players to replace the current primary player with the new one
    setSelectedPlayers((prev) => {
      // Remove the current primary player if they're in the list
      const filteredPlayers = prev.filter((p) => p.id !== primaryPlayer?.id);

      // Check if the new primary player is already in the filtered list
      const isNewPlayerAlreadyInList = filteredPlayers.some(
        (p) => p.id === newPrimaryPlayer.id
      );

      if (isNewPlayerAlreadyInList) {
        // If new player is already in the list, just move them to the front
        const otherPlayers = filteredPlayers.filter(
          (p) => p.id !== newPrimaryPlayer.id
        );
        return [newPrimaryPlayer, ...otherPlayers];
      } else {
        // If new player is not in the list, add them at the beginning
        return [newPrimaryPlayer, ...filteredPlayers];
      }
    });
  };

  // Initialize primary player with current user
  useEffect(() => {
    if (players.length > 0 && !primaryPlayer) {
      const currentUser = players.find(
        (player) => player.id === parseInt(user_id)
      );
      if (currentUser) {
        setPrimaryPlayer(currentUser);
      }
    }
  }, [players, primaryPlayer, user_id]);

  return (
    <div className="">
      <WarningModal
        isOpen={warningModal.isOpen}
        onClose={() => setWarningModal({ ...warningModal, isOpen: false })}
        title={warningModal.title}
        message={warningModal.message}
        actionButtonText={warningModal.actionButtonText}
        actionButtonLink={warningModal.actionButtonLink}
        type={warningModal.type}
      />

      {isLoading && <LoadingSpinner />}
      <div className="flex items-center justify-center bg-white p-4">
        {currentStep === 1 && (
          <div className=" ">Step 1 • Select date and time</div>
        )}
        {currentStep === 2 && (
          <div className=" ">Step 2 • Reserving details</div>
        )}
        {currentStep === 3 && <div className=" ">Step 3 • Payment</div>}
      </div>
      <div className="p-4">
        <BackButton
          onBack={() => {
            if (currentStep === 1) {
              navigate(-1);
            } else if (currentStep === 2) {
              setCurrentStep(1);
            } else {
              setCurrentStep(2);
            }
          }}
        />

        {currentStep === 1 && (
          <div>
            <div className=" p-4">
              <div className="space-y-6">
                <div className="mx-auto max-w-7xl p-4">
                  <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                    <SportTypeSelection
                      sports={sports}
                      onSelectionChange={({ sport, type, subType }) => {
                        setSelectedSport(sport);
                        setSelectedType(type);
                        setSelectedSubType(subType);
                      }}
                    />

                    <div className="h-fit rounded-lg bg-white p-4 shadow-5">
                      {userMembershipPlan?.advance_booking_enabled?.buddy ===
                      false ? (
                        <div className="mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
                          You can create a find-a-buddy request for any future
                          date.
                        </div>
                      ) : (
                        userMembershipPlan?.advance_booking_days?.buddy !==
                          undefined && (
                          <div className="mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
                            You can create a find-a-buddy request up to{" "}
                            {userMembershipPlan?.advance_booking_days?.buddy}{" "}
                            {userMembershipPlan?.advance_booking_days?.buddy ===
                            1
                              ? "day"
                              : "days"}{" "}
                            in advance.
                          </div>
                        )
                      )}
                      <Calendar
                        currentMonth={currentMonth}
                        selectedDate={selectedDate}
                        onDateSelect={(date) => {
                          // Check if date is within allowed range
                          if (
                            date > maxAllowedDate &&
                            userMembershipPlan?.advance_booking_enabled
                              ?.buddy !== false
                          ) {
                            const advanceDays =
                              userMembershipPlan?.advance_booking_days?.buddy ||
                              10;
                            showToast(
                              globalDispatch,
                              `Your membership plan only allows booking ${advanceDays} days in advance`,
                              3000,
                              "warning"
                            );
                            return;
                          }
                          setSelectedDate(date);
                        }}
                        onPreviousMonth={handlePreviousMonth}
                        onNextMonth={handleNextMonth}
                        daysOff={
                          club?.days_off ? JSON.parse(club.days_off) : []
                        }
                        allowPastDates={false}
                        minDate={new Date()}
                        maxDate={maxAllowedDate}
                        disabledDateMessage={
                          userMembershipPlan?.advance_booking_enabled?.buddy ===
                          false
                            ? "You can book for any future date"
                            : `Your membership plan only allows booking ${
                                userMembershipPlan?.advance_booking_days
                                  ?.buddy || 10
                              } days in advance`
                        }
                      />
                    </div>

                    {/* {selectedDate && (

                  )} */}
                    <TimeSlots
                      isLoading={searchLoading}
                      selectedDate={selectedDate}
                      timeRange={selectedTimes}
                      onTimeClick={handleTimeClick}
                      onTimeSlotsChange={handleTimeSlotsChange}
                      onNext={() => {
                        // Double-check that the selected date is within the allowed range
                        if (
                          selectedDate > maxAllowedDate &&
                          userMembershipPlan?.advance_booking_enabled?.buddy !==
                            false
                        ) {
                          const advanceDays =
                            userMembershipPlan?.advance_booking_days?.buddy ||
                            10;
                          showToast(
                            globalDispatch,
                            `Your membership plan only allows booking ${advanceDays} days in advance`,
                            3000,
                            "warning"
                          );
                          return;
                        }
                        handleNextPlayers();
                      }}
                      nextButtonText="Next: Players"
                      startHour={0}
                      clubTimes={club?.times ? JSON.parse(club.times) : []}
                      endHour={24}
                      interval={30}
                      isTimeSlotAvailable={() => true}
                      multipleSlots={true}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {currentStep === 2 && (
          <div className="mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3">
            <BuddyRequestReservationSummary
              selectedSport={selectedSport}
              sports={sports}
              selectedType={selectedType}
              selectedSubType={selectedSubType}
              selectedDate={selectedDate}
              selectedTimes={selectedTimes}
              timeSlots={timeSlots}
            />

            <div className="space-y-4">
              {/* Primary Player Selection */}
              {familyMembers.length > 0 && (
                <div className="h-fit rounded-lg bg-white p-4 shadow-5">
                  <label className="mb-2 block text-sm font-medium text-gray-900">
                    Request for
                  </label>
                  <Select
                    className="w-full text-sm"
                    options={[
                      {
                        value: players.find((p) => p.id === parseInt(user_id)),
                        label: `${
                          players.find((p) => p.id === parseInt(user_id))
                            ?.first_name
                        } ${
                          players.find((p) => p.id === parseInt(user_id))
                            ?.last_name
                        } (You)`,
                      },
                      ...familyMembers.map((member) => ({
                        value: member,
                        label: `${member.first_name} ${member.last_name} (${
                          member.family_role || "Family Member"
                        })`,
                      })),
                    ]}
                    onChange={handlePrimaryPlayerChange}
                    value={
                      primaryPlayer
                        ? {
                            value: primaryPlayer,
                            label:
                              primaryPlayer.id === parseInt(user_id)
                                ? `${primaryPlayer.first_name} ${primaryPlayer.last_name} (You)`
                                : `${primaryPlayer.first_name} ${
                                    primaryPlayer.last_name
                                  } (${
                                    primaryPlayer.family_role || "Family Member"
                                  })`,
                          }
                        : null
                    }
                    placeholder="Select who this request is for"
                    isSearchable={false}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Choose who this buddy request is for
                  </p>
                </div>
              )}

              <AddPlayers
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                selectedPlayers={selectedPlayers}
                setSelectedPlayers={setSelectedPlayers}
                players={players}
                groups={groups}
                selectedGroup={selectedGroup}
                isFindBuddyEnabled={isFindBuddyEnabled}
                setIsFindBuddyEnabled={setIsFindBuddyEnabled}
                playersNeeded={playersNeeded}
                handleIncrement={handleIncrement}
                handleDecrement={handleDecrement}
                onPlayerToggle={togglePlayer}
                showAddReservationToFindBuddy={false}
                showPlayersNeeded={false}
                familyMembers={familyMembers}
                currentUser={primaryPlayer}
                onCurrentUserChange={handlePrimaryPlayerChange}
                userProfile={players.find((p) => p.id === parseInt(user_id))}
              />
            </div>

            <div className="h-fit rounded-xl bg-white shadow-5">
              <div className="rounded-xl bg-gray-50 p-4 text-center">
                <h2 className="text-base font-medium">Other details</h2>
              </div>
              <div className="p-4">
                <div className="space-y-6">
                  {/* NTRP Score */}
                  <div>
                    <h3 className="mb-2 text-base font-medium">
                      My group NTRP score
                    </h3>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white">
                          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                            Min
                          </div>
                          <select
                            value={ntrpMin}
                            onChange={(e) => setNtrpMin(e.target.value)}
                            className="w-full appearance-none rounded-lg border-0 py-3 pl-12 pr-8 text-right focus:ring-0"
                          >
                            {ntrpOptions.map((option) => (
                              <option key={option} value={option}>
                                {option.toFixed(1)}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white">
                          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                            Max
                          </div>
                          <select
                            value={ntrpMax}
                            onChange={(e) => setNtrpMax(e.target.value)}
                            className="w-full appearance-none rounded-lg border-0 py-3 pl-12 pr-8 text-right focus:ring-0"
                          >
                            {ntrpOptions.map((option) => (
                              <option key={option} value={option}>
                                {option.toFixed(1)}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Players playing */}
                  <div className="mb-2 flex items-center justify-between gap-2">
                    <h3 className=" text-base font-medium">Players playing</h3>
                    <div className="">
                      <select
                        value={playersPlaying}
                        onChange={(e) => setPlayersPlaying(e.target.value)}
                        className="w-fit appearance-none rounded-xl border border-gray-200 bg-white py-2 pl-3 pr-8"
                      >
                        {[...Array(10)].map((_, index) => (
                          <option key={index} value={index + 1}>
                            {index + 1}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Players needed */}
                  <div className="mb-2 flex items-center justify-between gap-2">
                    <h3 className=" text-base font-medium">Players needed</h3>
                    <div className="relative">
                      <select
                        value={playersNeeded}
                        onChange={(e) => setPlayersNeeded(e.target.value)}
                        className="w-full appearance-none rounded-xl border border-gray-200 bg-white py-2 pl-3 pr-8"
                      >
                        {[...Array(10)].map((_, index) => (
                          <option key={index} value={index + 1}>
                            {index + 1}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Short bio */}
                  <div>
                    <h3 className="mb-2 text-base font-medium">
                      Short bio{" "}
                      <span className="text-gray-400">(Optional)</span>
                    </h3>
                    <textarea
                      className="w-full rounded-xl border border-gray-200 p-3 focus:border-gray-200 focus:ring-0"
                      rows="3"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                    ></textarea>
                  </div>

                  {/* Submit button */}
                  <InteractiveButton
                    loading={isSubmitting}
                    onClick={handleSave}
                    className="w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90"
                  >
                    Submit request
                  </InteractiveButton>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserAddFindABuddyRequest;
