import { useState, useEffect, useContext } from "react";
import { format } from "date-fns";
import {
  calculateServiceFee,
  getTimeRange,
  reservationTypes,
  calculateCoachTotalFees,
  activityLogTypes,
  actionLogTypes,
} from "Utils/utils";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import { Link } from "react-router-dom";
import { BsArrowDownUp } from "react-icons/bs";
import { BiSearch } from "react-icons/bi";
import AddPlayers from "Components/Players/AddPlayers";
import BottomDrawer from "Components/Drawers/BottomDrawer";
import { BackButton } from "Components/BackButton";
import { useNavigate } from "react-router-dom";
import { LessonReservationSummary } from "Components/Reservation/ReservationSummary";
import CheckoutForm from "Components/PaymentForm";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import TreeSDK from "Utils/TreeSDK";
import { useClub } from "Context/Club";
import moment from "moment";
import Select from "react-select";

const sdk = new MkdSDK();
const tdk = new TreeSDK();
export default function TimeTabSelectCoach({
  coaches,
  onClose,
  selectedDate,
  selectedSport,
  sports,
  players,
  groups,
  isOpen,
  selectedType,
  selectedSubType,
  selectedTimes,
  userProfile,
}) {
  const [sortAscending, setSortAscending] = useState(true);
  const [selectedCoach, setSelectedCoach] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeStep, setActiveStep] = useState(1);
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  const [playersNeeded, setPlayersNeeded] = useState(1);
  const [isFindBuddyEnabled, setIsFindBuddyEnabled] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [primaryPlayer, setPrimaryPlayer] = useState(null);
  const navigate = useNavigate();
  const { duration, end_time, start_time } = getTimeRange(selectedTimes);
  const [clientSecret, setClientSecret] = useState(null);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [bookingId, setBookingId] = useState(null);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [reservationId, setReservationId] = useState(null);
  const { user_subscription, user_permissions, club } = useClub();
  const user_id = localStorage.getItem("user");
  console.log("selectedTimes", selectedTimes);

  const handleIncrement = () => {
    setPlayersNeeded((prev) => Math.min(prev + 1, 10));
  };

  const handleDecrement = () => {
    setPlayersNeeded((prev) => Math.max(prev - 1, 1));
  };

  const fetchFamilyMembers = async () => {
    try {
      const familyResponse = await tdk.getList("user", {
        filter: [`guardian,eq,${user_id}`, `role,cs,user`],
      });
      setFamilyMembers(familyResponse.list);
    } catch (error) {
      console.error("Error fetching family members:", error);
    }
  };

  const handlePrimaryPlayerChange = (selectedOption) => {
    // Handle both Select component (with .value) and direct user object
    const newPrimaryPlayer = selectedOption.value || selectedOption;

    // Don't do anything if the same player is selected
    if (newPrimaryPlayer?.id === primaryPlayer?.id) {
      return;
    }

    setPrimaryPlayer(newPrimaryPlayer);

    // Update selected players to replace the current primary player with the new one
    setSelectedPlayers((prev) => {
      // Remove the current primary player if they're in the list
      const filteredPlayers = prev.filter((p) => p.id !== primaryPlayer?.id);

      // Check if the new primary player is already in the filtered list
      const isNewPlayerAlreadyInList = filteredPlayers.some(
        (p) => p.id === newPrimaryPlayer.id
      );

      if (isNewPlayerAlreadyInList) {
        // If new player is already in the list, just move them to the front
        const otherPlayers = filteredPlayers.filter(
          (p) => p.id !== newPrimaryPlayer.id
        );
        return [newPrimaryPlayer, ...otherPlayers];
      } else {
        // If new player is not in the list, add them at the beginning
        return [newPrimaryPlayer, ...filteredPlayers];
      }
    });
  };

  // Initialize primary player and fetch family members
  React.useEffect(() => {
    if (userProfile && !primaryPlayer) {
      setPrimaryPlayer(userProfile);
    }
    fetchFamilyMembers();
  }, [userProfile, primaryPlayer]);

  const selectedHours = selectedTimes.reduce((total, timeSlot) => {
    const fromTime = new Date(`2000/01/01 ${timeSlot.from}`);
    const untilTime = new Date(`2000/01/01 ${timeSlot.until}`);
    const durationInHours = (untilTime - fromTime) / (1000 * 60 * 60);
    return total + durationInHours;
  }, 0);

  const fees = calculateCoachTotalFees({
    hourlyRate: selectedCoach?.hourly_rate,
    hours: selectedHours,
    playerCount: selectedPlayers.length,
    feeSettings: club?.fee_settings,
  });

  const handleMakeReservation = async () => {
    if (!user_subscription?.planId) {
      showToast(
        globalDispatch,
        "Please subscribe to a membership plan to book lessons with coaches",
        3000,
        "error"
      );
      return;
    }

    if (!user_permissions?.allowCoach) {
      showToast(
        globalDispatch,
        `Your current plan (${user_permissions?.planName}) does not include coach lessons. Please upgrade your plan.`,
        3000,
        "error"
      );
      return;
    }

    if (!selectedSport || !selectedDate || !selectedTimes || !selectedPlayers) {
      showToast(
        globalDispatch,
        "Please select all required fields",
        3000,
        "error"
      );
      return;
    }

    setIsSubmitting(true);
    try {
      const paymentIntentResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",
        { amount: fees.total },
        "POST"
      );
      if (paymentIntentResponse.error) {
        showToast(globalDispatch, paymentIntentResponse.message, 3000, "error");
        setIsSubmitting(false);
        return;
      }
      setClientSecret(paymentIntentResponse.client_secret);
      setPaymentIntent(paymentIntentResponse.payment_intent);
      const formattedDate = moment(selectedDate).format("YYYY-MM-DD");
      const formData = {
        sport_id: selectedSport,
        type: selectedType,
        sub_type: selectedSubType,
        date: formattedDate,
        player_ids: selectedPlayers.map((p) => p.id),
        primary_player_id: primaryPlayer?.id || userProfile?.id, // Add primary player ID
        start_time: start_time,
        end_time: end_time,
        price: fees.total,
        coach_fee: fees.coachFee,
        service_fee: fees.serviceFee,
        duration: duration,
        coach_id: selectedCoach.id,
        payment_intent: paymentIntentResponse.payment_intent,
        reservation_type: reservationTypes.lesson,
      };

      const response = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations",
        formData,
        "POST"
      );

      // Create activity log
      sdk.setTable("activity_logs");
      await sdk.callRestAPI(
        {
          user_id: user_id,
          activity_type: activityLogTypes.lesson,
          action_type: actionLogTypes.CREATE,
          data: JSON.stringify(formData),
          club_id: club?.id,
          description: "Created a lesson reservation",
        },
        "POST"
      );

      if (!response.error) {
        showToast(
          globalDispatch,
          "Reservation created successfully",
          3000,
          "success"
        );
        setReservationId(response.reservation_id);
        setBookingId(response.booking_id);
        setActiveStep(3);
      }
    } catch (error) {
      console.error(error);
      showToast(
        globalDispatch,
        error.message || "Error creating reservation",
        3000,
        "error"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePlayer = (player) => {
    setSelectedPlayers((prev) => {
      const isSelected = prev.some((p) => p.id === player.id);
      if (isSelected) {
        return prev.filter((p) => p.id !== player.id);
      }
      return [...prev, player];
    });
  };

  const handleContinue = () => {
    if (selectedCoach) {
      setActiveStep(2);
    }
  };

  const lessonReservationDescription = club?.lesson_description
    ? JSON.parse(club?.lesson_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <BottomDrawer
      onClose={onClose}
      isOpen={isOpen}
      title={activeStep === 1 ? "Select Coach" : "Reservation detail"}
    >
      <div className="relative mx-auto h-[90vh] w-full max-w-7xl overflow-y-auto rounded-lg ">
        <BackButton
          onBack={() => {
            if (activeStep === 1) {
              setActiveStep(2);
            } else {
              onClose();
            }
          }}
        />
        {activeStep === 1 && (
          <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
            <div className="max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5">
              {/* Search Bar */}
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search by name"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"
                />
                <BiSearch className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
                <button
                  onClick={() => setSortAscending(!sortAscending)}
                  className="absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50"
                >
                  <div className="flex items-center gap-1">
                    <span>A-Z</span>
                    <BsArrowDownUp
                      className={`text-xs transition-transform ${
                        !sortAscending ? "rotate-180" : ""
                      }`}
                    />
                  </div>
                </button>
              </div>

              {/* Coach Listings */}
              <div className="max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto">
                {coaches.length > 0 &&
                  coaches
                    .filter((coach) => {
                      const fullName =
                        `${coach?.first_name} ${coach?.last_name}`.toLowerCase();
                      return fullName.includes(searchQuery.toLowerCase());
                    })
                    .sort((a, b) => {
                      const nameA =
                        `${a?.first_name} ${a?.last_name}`.toLowerCase();
                      const nameB =
                        `${b?.first_name} ${b?.last_name}`.toLowerCase();
                      return sortAscending
                        ? nameA.localeCompare(nameB)
                        : nameB.localeCompare(nameA);
                    })
                    .map((coach) => (
                      <div
                        key={coach.id}
                        className={`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${
                          selectedCoach?.id === coach.id
                            ? "border-primaryBlue bg-blue-50"
                            : "border-gray-100 hover:bg-gray-50"
                        }`}
                        onClick={() => setSelectedCoach(coach)}
                      >
                        <div className="flex items-center gap-3">
                          <img
                            src={
                              coach?.photo ||
                              coach?.photo ||
                              `/default-avatar.png`
                            }
                            alt={`${coach?.first_name} ${coach?.last_name}`}
                            className="h-10 w-10 rounded-full object-cover"
                          />
                          <div className="flex flex-col">
                            <span className="font-medium capitalize">
                              {coach?.first_name} {coach?.last_name}
                            </span>
                          </div>
                        </div>
                        <span className="text-gray-600">
                          {fCurrency(coach.hourly_rate)}/h
                        </span>
                      </div>
                    ))}
                {coaches.length === 0 && (
                  <p className="text-center text-sm text-gray-500">
                    No coaches found
                  </p>
                )}
              </div>
            </div>

            <div>
              {selectedCoach && (
                <div className="h-fit rounded-lg bg-white shadow-5">
                  <div className="space-y-6 p-4">
                    <div className="flex items-center justify-between">
                      <h2 className="text-xl font-semibold">Coach Profile</h2>
                    </div>

                    <div className="flex items-center gap-4">
                      <img
                        src={
                          selectedCoach?.photo ||
                          selectedCoach?.photo ||
                          `/default-avatar.png`
                        }
                        alt={`${selectedCoach?.first_name} ${selectedCoach?.last_name}`}
                        className="h-16 w-16 rounded-lg object-cover"
                      />
                      <div>
                        <h3 className="text-lg font-medium capitalize">
                          {selectedCoach?.first_name} {selectedCoach?.last_name}
                        </h3>
                        <p className="text-lg text-gray-600">
                          {fCurrency(selectedCoach?.hourly_rate)}/h
                        </p>
                      </div>
                    </div>

                    {selectedCoach?.bio && (
                      <div className="space-y-2">
                        <p className="text-gray-600">{selectedCoach?.bio}</p>
                      </div>
                    )}
                  </div>

                  <div className="border-t p-3">
                    <button
                      onClick={handleContinue}
                      className="rounded-lg bg-blue-900 px-4 py-2 text-white hover:bg-blue-800"
                    >
                      Continue with {selectedCoach?.first_name}
                    </button>
                  </div>
                </div>
              )}

              {/* <div className=" mt-3 h-full flex-col gap-4">
                <button
                  onClick={() => setActiveStep(2)}
                  className="b rounded-lg border border-primaryBlue px-4 py-2 text-sm text-primaryBlue "
                >
                  Continue without coach
                </button>
              </div> */}
            </div>
          </div>
        )}

        {activeStep === 2 && (
          <div className="mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3">
            {/* Summary Card */}
            <LessonReservationSummary
              selectedSport={selectedSport}
              sports={sports}
              selectedType={selectedType}
              selectedSubType={selectedSubType}
              selectedDate={selectedDate}
              timeRange={getTimeRange(selectedTimes)}
              playersNeeded={playersNeeded}
              selectedCoach={selectedCoach}
            />

            <div className="space-y-4">
              {/* Primary Player Selection */}
              {familyMembers.length > 0 && (
                <div className="h-fit rounded-lg bg-white p-4 shadow-5">
                  <label className="mb-2 block text-sm font-medium text-gray-900">
                    Book lesson for
                  </label>
                  <Select
                    className="w-full text-sm"
                    options={[
                      {
                        value: userProfile,
                        label: `${userProfile?.first_name} ${userProfile?.last_name} (You)`,
                      },
                      ...familyMembers.map((member) => ({
                        value: member,
                        label: `${member.first_name} ${member.last_name} (${
                          member.family_role || "Family Member"
                        })`,
                      })),
                    ]}
                    onChange={handlePrimaryPlayerChange}
                    value={
                      primaryPlayer
                        ? {
                            value: primaryPlayer,
                            label:
                              primaryPlayer.id === userProfile?.id
                                ? `${primaryPlayer.first_name} ${primaryPlayer.last_name} (You)`
                                : `${primaryPlayer.first_name} ${
                                    primaryPlayer.last_name
                                  } (${
                                    primaryPlayer.family_role || "Family Member"
                                  })`,
                          }
                        : null
                    }
                    placeholder="Select who this lesson is for"
                    isSearchable={false}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Choose who this lesson is for
                  </p>
                </div>
              )}

              {/* Players Selection */}
              <AddPlayers
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                selectedPlayers={selectedPlayers}
                setSelectedPlayers={setSelectedPlayers}
                onPlayerToggle={togglePlayer}
                players={players}
                groups={groups}
                selectedGroup={selectedGroup}
                isFindBuddyEnabled={isFindBuddyEnabled}
                setIsFindBuddyEnabled={setIsFindBuddyEnabled}
                playersNeeded={playersNeeded}
                handleIncrement={handleIncrement}
                handleDecrement={handleDecrement}
                familyMembers={familyMembers}
                currentUser={primaryPlayer}
                onCurrentUserChange={handlePrimaryPlayerChange}
                userProfile={userProfile}
              />
            </div>

            {/* Reservation detail */}
            <div className="h-fit rounded-lg bg-white shadow-5">
              <div className="rounded-lg bg-gray-50 p-4 text-center">
                <h2 className="text-base font-medium">Reserving Details</h2>
              </div>
              <div className="p-4">
                <div className="space-y-2">
                  <div className="divide-y">
                    <div className="py-3">
                      <p className="text-sm text-gray-500">
                        PLAYERS ({selectedPlayers.length})
                      </p>
                      <div className="mt-1">
                        {selectedPlayers.map((player) => (
                          <div key={player.id} className="text-sm">
                            {player.first_name} {player.last_name}
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="py-3">
                      <p className="text-sm text-gray-500">COACH</p>
                      <div className="mt-1">
                        <div className="text-sm">
                          {selectedCoach?.first_name} {selectedCoach?.last_name}
                        </div>
                      </div>
                    </div>

                    <div className="py-3">
                      <p className="text-sm text-gray-500">FEES</p>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="flex items-center gap-1">
                          Coach fee
                        </span>
                        <span>{fCurrency(fees.coachFee)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Service fee</span>
                        <span>{fCurrency(fees.serviceFee)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between border-t pt-4">
                    <span>Total</span>
                    <span className="font-medium">{fCurrency(fees.total)}</span>
                  </div>

                  <div className="rounded-lg bg-[#F17B2C] p-3 text-sm text-white">
                    <div className="flex items-start gap-2">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 8 2.33325C8.10005 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z"
                          fill="white"
                        />
                      </svg>
                      <span>
                        After reserving, you will have 15 minutes to make the
                        payment.
                      </span>
                    </div>
                  </div>

                  <InteractiveButton
                    loading={isSubmitting}
                    onClick={handleMakeReservation}
                    className="w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90"
                  >
                    <div className="flex flex-col items-center">
                      <span>Reserve Now</span>
                      <span className="text-sm opacity-80">
                        and continue to payment
                      </span>
                    </div>
                  </InteractiveButton>

                  <p className="text-center text-sm text-gray-500">
                    {lessonReservationDescription.reservation_description}
                  </p>

                  {/* Additional note */}
                  <div className="space-y-2 text-center text-sm text-gray-500">
                    <p>(You will not be charged yet)</p>
                    <p>
                      For any issues, please contact our support team at{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="font-medium underline"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeStep === 3 && (
          <div className="mx-auto max-w-6xl">
            <div className="rounded-xl bg-[#F17B2C] px-4 py-3 text-white">
              <div className="flex items-center gap-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="!text-sm">
                  Your session is reserved. You have 15 minutes to complete the
                  payment, otherwise the reservation will be canceled.
                </span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
              <div className="space-y-6">
                <LessonReservationSummary
                  selectedSport={selectedSport}
                  sports={sports}
                  selectedType={selectedType}
                  selectedSubType={selectedSubType}
                  selectedDate={selectedDate}
                  timeRange={getTimeRange(selectedTimes)}
                  selectedCoach={selectedCoach}
                />
              </div>

              <div className="space-y-6">
                <div className="rounded-xl bg-white p-6 shadow-5">
                  <h2 className="mb-4 text-center text-lg font-medium">
                    Payment details
                  </h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500">Coach fee</span>
                      <span>
                        {fCurrency(selectedCoach?.hourly_rate)}/hr ×{" "}
                        {selectedHours}hr × {selectedPlayers.length} players
                      </span>
                      <span>
                        {fCurrency(
                          selectedCoach?.hourly_rate *
                            selectedHours *
                            selectedPlayers.length
                        )}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500">Service fee</span>
                      <span>
                        {fCurrency(
                          calculateServiceFee(
                            club?.fee_settings,
                            fees.baseAmount
                          )
                        )}
                      </span>
                    </div>
                    <div className="flex items-center justify-between border-t pt-4">
                      <span className="font-medium">Total</span>
                      <span className="font-medium">
                        {fCurrency(fees.total)}
                      </span>
                    </div>
                    <div>
                      <CheckoutForm
                        user={userProfile}
                        bookingId={bookingId}
                        reservationId={reservationId}
                        clientSecret={clientSecret}
                        paymentIntent={paymentIntent}
                        navigateRoute={`/user/payment-success/${reservationId}?type=lesson`}
                      />
                    </div>

                    <p className="text-center text-sm text-gray-500">
                      {lessonReservationDescription.payment_description}
                    </p>

                    <div className="space-y-4 text-sm text-gray-500">
                      <p>
                        By clicking "Pay now" you agree to our{" "}
                        <Link
                          to="/terms-and-conditions"
                          target="_blank"
                          className="font-medium underline"
                        >
                          Terms and Conditions
                        </Link>{" "}
                        and{" "}
                        <Link
                          to="/privacy-policy"
                          target="_blank"
                          className="font-medium underline"
                        >
                          Privacy Policy
                        </Link>
                        . All sales are final unless stated otherwise.
                      </p>
                      <p>
                        For any issues, please contact our support team at{" "}
                        <a
                          href="mailto:<EMAIL>"
                          className="font-medium underline"
                        >
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </BottomDrawer>
  );
}
