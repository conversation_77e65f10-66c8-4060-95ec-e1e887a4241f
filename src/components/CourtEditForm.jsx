import { useContext, useState, useEffect } from "react";
import { GlobalContext, showToast, getManyByIds } from "../context/Global";
import { AuthContext } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { InteractiveButton } from "./InteractiveButton";
import OverlayLoading from "./Loading/OverlayLoading";
import RightSideModal from "./RightSideModal";
import LoadingSpinner from "./LoadingSpinner";
import ConfirmEventsDeleteModal from "./Modals/ConfirmEventsDeleteModal";

const sdk = new MkdSDK();

export default function CourtEditForm({
  onSubmit,
  initialData,
  onClose,
  isOpen,
  isEdit,
  club,
  sports = [],
  courts,
  // Additional props for the RightSideModal
  title,
  primaryButtonText,
  submitting,
  showFooter = false,
  onPrimaryAction,
}) {
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    sport_id: Number(initialData?.sport_id) || null,
    type: initialData?.type || "",
    sub_type: initialData?.sub_type || null,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [showSportChangeModal, setShowSportChangeModal] = useState(false);
  const [tempSportId, setTempSportId] = useState(null);
  const [sportChangeOption, setSportChangeOption] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [affectedEventCount, setAffectedEventCount] = useState(0);
  const [pendingData, setPendingData] = useState(null);
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name || "",
        sport_id: Number(initialData.sport_id) || null,
        type: initialData.type || "",
        sub_type: initialData.sub_type || null,
      });
    }
  }, [initialData]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormData((prev) => {
      const newData = {
        ...prev,
        [name]: name === "sport_id" ? Number(value) : value,
      };

      // Reset type and sub_type when sport changes
      if (name === "sport_id") {
        newData.type = "";
        newData.sub_type = null;
      }

      // Reset sub_type when type changes
      if (name === "type") {
        newData.sub_type = null;
      }

      return newData;
    });
  };

  const handleSportChangeConfirm = async () => {
    // Close modal first
    setShowSportChangeModal(false);

    // Now proceed with saving the changes
    setSubmitLoading(true);
    try {
      const submitData = {
        court_id: initialData.id,
        ...formData,
        sport_id: tempSportId, // Use the temp sport ID
        type: "", // Reset type when sport changes
        sub_type: null, // Reset sub_type when sport changes
        sport_change_option: sportChangeOption,
      };

      // Check for affected events if we're editing an existing court
      const hasAffectedEvents = await checkForAffectedEvents(submitData);

      if (hasAffectedEvents) {
        // If there are affected events, we'll wait for user confirmation
        setSubmitLoading(false);
        return;
      }

      // If no affected events, proceed with submission
      await onSubmit(submitData);
    } catch (error) {
      console.error(error);
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleSportChangeCancel = () => {
    // Reset and close modal without changing sport
    setTempSportId(null);
    setSportChangeOption(null);
    setShowSportChangeModal(false);
  };

  // Check for affected events when changing court details
  const checkForAffectedEvents = async (submitData) => {
    try {
      // Call dummy API to check for affected events
      // In a real implementation, this would call a real API endpoint
      await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/club/check-affected-events",
        {
          court_id: submitData.court_id,
          sport_id: submitData.sport_id,
          type: submitData.type,
          sub_type: submitData.sub_type,
        },
        "POST"
      );

      // For now, simulate a response with a random number of affected events
      // Remove this in production and use the actual API response
      const dummyResponse = {
        affected_events_count: Math.floor(Math.random() * 5), // Random number between 0-4
      };

      if (dummyResponse.affected_events_count > 0) {
        setAffectedEventCount(dummyResponse.affected_events_count);
        setPendingData(submitData);
        setShowConfirmModal(true);
        return true; // There are affected events
      }

      return false; // No affected events
    } catch (error) {
      console.error("Error checking for affected events:", error);
      return false;
    }
  };

  // Handle confirmation to proceed with changes
  const handleConfirmChanges = async () => {
    setSubmitLoading(true);
    try {
      await onSubmit(pendingData);
      setShowConfirmModal(false);
    } catch (error) {
      console.error("Error saving changes:", error);
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();

    // Validate type if sport has types
    if (sportTypes.length > 0 && !formData.type) {
      showToast(globalDispatch, "Please select a court type", 3000, "error");
      return;
    }

    // Validate subtype if type has subtypes
    if (sportSubTypes.length > 0 && !formData.sub_type) {
      showToast(globalDispatch, "Please select a surface type", 3000, "error");
      return;
    }

    // Check if sport has changed and we're in edit mode
    if (
      isEdit &&
      initialData?.sport_id &&
      formData.sport_id !== initialData.sport_id
    ) {
      // Store the new sport ID and show the sport change modal
      setTempSportId(formData.sport_id);
      setShowSportChangeModal(true);
      return;
    }

    setSubmitLoading(true);
    try {
      const submitData = isEdit
        ? {
            court_id: initialData.id,
            ...formData,
          }
        : formData;

      // Only check for affected events if we're editing an existing court
      if (isEdit) {
        const hasAffectedEvents = await checkForAffectedEvents(submitData);

        if (hasAffectedEvents) {
          // If there are affected events, we'll wait for user confirmation
          setSubmitLoading(false);
          return;
        }
      }

      // If no affected events or we're adding a new court, proceed with submission
      await onSubmit(submitData);
    } catch (error) {
      console.error(error);
    } finally {
      setSubmitLoading(false);
    }
  };

  // Get the selected sport's type and sub_type options
  const selectedSport = sports.find((sport) => sport.id === formData.sport_id);

  // Get available types for selected sport
  const sportTypes =
    selectedSport?.sport_types?.filter((type) => type.type) || [];

  // Get available subtypes for selected type
  const sportSubTypes =
    selectedSport?.sport_types?.find((st) => st.type === formData.type)
      ?.subtype || [];

  // Custom Sport Change Modal
  const SportChangeModal = () => {
    const [option, setOption] = useState("");

    const handleOptionChange = (e) => {
      setOption(e.target.value);
    };

    const handleConfirm = () => {
      // Convert option to number before setting it in the parent component
      setSportChangeOption(Number(option));
      handleSportChangeConfirm();
    };

    // Find the names of the old and new sports for display
    const oldSport =
      sports.find((sport) => sport.id === initialData?.sport_id)?.name ||
      "previous sport";
    const newSport =
      sports.find((sport) => sport.id === tempSportId)?.name || "new sport";

    return (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center">
        <div className="fixed inset-0 bg-black opacity-50"></div>
        <div className="relative z-50 w-full max-w-md rounded-lg bg-white p-6">
          <h3 className="mb-4 text-lg font-medium">Change Sport</h3>
          <p className="mb-4 text-gray-600">
            You are changing the sport for this space from{" "}
            <strong>{oldSport}</strong> to <strong>{newSport}</strong>. However,
            any events for this space will still be listed under the previous
            sport, including in the daily scheduler.
          </p>
          <p className="mb-4 text-gray-600">Would you like to:</p>

          <div className="mb-6 space-y-3">
            <label className="flex items-start gap-2">
              <input
                type="radio"
                name="sportChangeOption"
                value="1"
                checked={option === "1"}
                onChange={handleOptionChange}
                className="mt-1 h-4 w-4"
              />
              <span>
                A: Keep these events (events will remain under {oldSport})
              </span>
            </label>

            <label className="flex items-start gap-2">
              <input
                type="radio"
                name="sportChangeOption"
                value="2"
                checked={option === "2"}
                onChange={handleOptionChange}
                className="mt-1 h-4 w-4"
              />
              <span>B: Delete these events</span>
            </label>

            <label className="flex items-start gap-2">
              <input
                type="radio"
                name="sportChangeOption"
                value="3"
                checked={option === "3"}
                onChange={handleOptionChange}
                className="mt-1 h-4 w-4"
              />
              <span>
                C: Change the sport listed for these events to {newSport}
              </span>
            </label>
          </div>

          <div className="flex justify-end gap-3">
            <button
              onClick={handleSportChangeCancel}
              className="rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={!option}
              className={`rounded-lg px-4 py-2 text-white ${
                option
                  ? "bg-primaryBlue hover:bg-blue-700"
                  : "cursor-not-allowed bg-gray-400"
              }`}
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Form content component to avoid duplication
  const CourtForm = () => (
    <form onSubmit={handleFormSubmit} className="flex h-full flex-col">
      <div className="h-full flex-1 space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Court Name
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter court name"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Sport
          </label>
          <div className="mt-2 space-x-4">
            {sports
              .filter((sport) => sport.status === 1)
              .map((sport) => (
                <label key={sport.id} className="inline-flex items-center">
                  <input
                    type="radio"
                    name="sport_id"
                    value={sport.id}
                    checked={formData.sport_id === sport.id}
                    onChange={handleChange}
                    className="form-radio"
                  />
                  <span className="ml-2">{sport.name}</span>
                </label>
              ))}
          </div>
        </div>

        {sportTypes.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Type <span className="text-red-500">*</span>
            </label>
            <div className="mt-2 space-x-4">
              {sportTypes.map((sportType) => (
                <label
                  key={sportType.type}
                  className="inline-flex items-center"
                >
                  <input
                    type="radio"
                    name="type"
                    value={sportType.type}
                    checked={formData.type === sportType.type}
                    onChange={handleChange}
                    className="form-radio"
                    required
                  />
                  <span className="ml-2">{sportType.type}</span>
                </label>
              ))}
            </div>
          </div>
        )}

        {formData.type && sportSubTypes.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Sub Type <span className="text-red-500">*</span>
            </label>
            <div className="mt-2 space-x-4">
              {sportSubTypes.map((subType) => (
                <label key={subType} className="inline-flex items-center">
                  <input
                    type="radio"
                    name="sub_type"
                    value={subType}
                    checked={formData.sub_type === subType}
                    onChange={handleChange}
                    className="form-radio"
                    required
                  />
                  <span className="ml-2">{subType}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>
      <div className="flex flex-shrink-0 justify-end gap-4 border-t border-gray-200 px-4 py-4">
        <button
          type="button"
          className="flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50"
          onClick={onClose}
        >
          Cancel
        </button>
        <InteractiveButton
          type="submit"
          loading={submitLoading}
          className="flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700"
        >
          {isEdit ? "Save changes" : "Add court"}
        </InteractiveButton>
      </div>
    </form>
  );

  return (
    <div>
      {isLoading && <LoadingSpinner />}
      <RightSideModal
        isOpen={isOpen !== undefined ? isOpen : true}
        onClose={onClose}
        title={title || (isEdit ? "Edit court" : "Add new court")}
        showFooter={showFooter}
        primaryButtonText={
          primaryButtonText || (isEdit ? "Save changes" : "Add court")
        }
        onPrimaryAction={onPrimaryAction}
        submitting={submitting || submitLoading}
      >
        <OverlayLoading isLoading={isLoading}>
          <CourtForm />
          {/* Sport Change Confirmation Modal */}
        </OverlayLoading>
      </RightSideModal>
      {showSportChangeModal && <SportChangeModal />}

      {/* Confirmation Modal for Affected Events */}
      <ConfirmEventsDeleteModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirmChanges}
        eventCount={affectedEventCount}
        isSubmitting={submitLoading}
        type="court"
      />
    </div>
  );
}
