import { Switch } from "@headlessui/react";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { FaArrowUpRightFromSquare } from "react-icons/fa6";
import React, { useEffect, useState } from "react";
import RightSideModal from "Components/RightSideModal";
import PlanDetailsModal from "Components/PlanDetailsModal";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { fCurrency } from "Utils/formatNumber";
import LoadingSpinner from "Components/LoadingSpinner";
import { actionLogTypes, activityLogTypes } from "Utils/utils";
import { useClub } from "Context/Club";
let sdk = new MkdSDK();

export default function MembershipModules({
  fetchProfileSettings,
  membershipPlans,
  profileSettings,
  role,
}) {
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentMode, setcurrentMode] = useState("");
  const [isShaking, setIsShaking] = useState(false);
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { club, sports } = useClub();
  const user_id = localStorage.getItem("user");
  const { dispatch: globalDispatch, state: globalState } =
    React.useContext(GlobalContext);

  // Helper function to get sport names from IDs
  const getSportNames = (sportIds) => {
    if (!sportIds || sportIds.length === 0) return "All Sports";
    if (!sports || sports.length === 0) return "Loading...";

    const sportNames = sportIds
      .map((id) => sports.find((sport) => sport.id === id)?.name)
      .filter(Boolean);

    if (sportNames.length === 0) return "No Sports";
    if (sportNames.length > 2) {
      return `${sportNames.slice(0, 2).join(", ")} +${
        sportNames.length - 2
      } more`;
    }
    return sportNames.join(", ");
  };

  const handleSwitchClick = (e) => {
    e.preventDefault();
    setIsShaking(true);
    showToast(
      globalDispatch,
      "Please use the edit button to modify plan settings",
      3000,
      "warning"
    );
    setTimeout(() => setIsShaking(false), 820);
  };

  const handlePlanClick = (plan) => {
    setSelectedPlan(plan);
    setIsModalOpen(true);
  };

  const handlePlanCreate = async (newPlan, mode) => {
    //     stripe_product has club_id
    // stripe _price has product_id
    // Stripe price is the plans table
    // setMembershipPlans((plans) => [...plans, newPlan]);

    try {
      let formData;

      if (mode === "edit") {
        // For edit, we need to send all plans with the updated one
        formData = {
          membership_settings: membershipPlans.map((plan) =>
            plan.plan_id === newPlan.plan_id
              ? {
                  plan_id: newPlan.plan_id,
                  plan_name: newPlan.plan_name,
                  price: newPlan.price,
                  allow_clinic: newPlan.allow_clinic,
                  allow_buddy: newPlan.allow_buddy,
                  allow_coach: newPlan.allow_coach,
                  allow_groups: newPlan.allow_groups,
                  allow_court: newPlan.allow_court,
                  features: newPlan.features,
                  applicable_sports: newPlan.applicable_sports || [],
                  advance_booking_days: {
                    court: newPlan.advance_booking_days?.court || 10,
                    lesson: newPlan.advance_booking_days?.lesson || 10,
                    clinic: newPlan.advance_booking_days?.clinic || 10,
                    buddy: newPlan.advance_booking_days?.buddy || 10,
                  },
                  advance_booking_enabled: {
                    court:
                      newPlan.advance_booking_enabled?.court === false
                        ? false
                        : true,
                    lesson:
                      newPlan.advance_booking_enabled?.lesson === false
                        ? false
                        : true,
                    clinic:
                      newPlan.advance_booking_enabled?.clinic === false
                        ? false
                        : true,
                    buddy:
                      newPlan.advance_booking_enabled?.buddy === false
                        ? false
                        : true,
                  },
                }
              : plan
          ),
        };
      } else {
        const stripeProductResponse = await sdk.callRawAPI(
          `/v3/api/custom/courtmatchup/stripe/product`,
          {
            name: newPlan.plan_name,
            description: newPlan.plan_name,
            club_id: globalState.clubProfile?.club?.id,
          },
          "POST"
        );
        console.log("stripeProductResponse", stripeProductResponse);

        const stripePriceResponse = await sdk.callRawAPI(
          `/v3/api/custom/courtmatchup/stripe/price`,
          {
            product_id: stripeProductResponse.model,
            name: newPlan.plan_name,
            amount: newPlan.price,
            type: "recurring",
            interval: "month",
            interval_count: 1,
            trial_days: 0,
            usage_type: "licenced",
            usage_limit: 0,
          },
          "POST"
        );

        // For create, we append the new plan to existing plans
        formData = {
          membership_settings: [
            ...membershipPlans,
            {
              plan_id: membershipPlans.length + 1, // Generate new ID
              plan_name: newPlan.plan_name,
              price: newPlan.price,
              allow_clinic: newPlan.allow_clinic,
              allow_buddy: newPlan.allow_buddy,
              allow_coach: newPlan.allow_coach,
              allow_groups: newPlan.allow_groups,
              allow_court: newPlan.allow_court,
              features: newPlan.features,
              applicable_sports: newPlan.applicable_sports || [],
              advance_booking_days: {
                court: newPlan.advance_booking_days?.court || 10,
                lesson: newPlan.advance_booking_days?.lesson || 10,
                clinic: newPlan.advance_booking_days?.clinic || 10,
                buddy: newPlan.advance_booking_days?.buddy || 10,
              },
              advance_booking_enabled: {
                court: newPlan.advance_booking_enabled?.court || true,
                lesson: newPlan.advance_booking_enabled?.lesson || true,
                clinic: newPlan.advance_booking_enabled?.clinic || true,
                buddy: newPlan.advance_booking_enabled?.buddy || true,
              },
            },
          ],
        };
      }

      console.log(
        "Submitting membership plan data:",
        JSON.stringify(formData, null, 2)
      );

      const response = await sdk.callRawAPI(
        role === "club"
          ? `/v3/api/custom/courtmatchup/club/profile-edit`
          : `/v3/api/custom/courtmatchup/admin/profile-edit/${profileSettings?.user?.id}`,
        formData,
        "POST"
      );
      // Create activity log
      sdk.setTable("activity_logs");
      await sdk.callRestAPI(
        {
          user_id: user_id,
          activity_type: activityLogTypes.club_ui,
          action_type: actionLogTypes.UPDATE,
          data: JSON.stringify(formData),
          club_id: club?.id,
          description: "Updated membership plans",
        },
        "POST"
      );
      if (response.error) {
        showToast(
          globalDispatch,
          response.message || "Failed to save plan",
          3000,
          "error"
        );
      }

      setIsModalOpen(false);
      fetchProfileSettings();
      handleCloseModal();
    } catch (e) {
      showToast(
        globalDispatch,
        e.message || "Failed to save plan",
        3000,
        "error"
      );
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPlan(null);
  };

  return (
    <div className="flex flex-col gap-4 p-5">
      <style>
        {`
          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
            20%, 40%, 60%, 80% { transform: translateX(4px); }
          }
          .shake {
            animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
          }
        `}
      </style>
      {isLoading && <LoadingSpinner />}
      <div className="flex items-center justify-between">
        <div className="text-xl font-medium">Membership settings</div>
        <button
          onClick={() => {
            setcurrentMode("create");
            setIsModalOpen(true);
          }}
          className="flex items-center gap-2 rounded-lg border bg-primaryBlue px-3 py-2 text-sm text-white "
        >
          <span>New plan</span>
          <ChevronRightIcon className="h-4 w-4" />
        </button>
      </div>

      <div className={`overflow-x-auto ${isShaking ? "shake" : ""}`}>
        <table className="min-w-full border-separate border-spacing-y-2">
          <thead>
            <tr className="text-left text-sm text-gray-500">
              <th className="pb-4">Plan</th>
              <th className="pb-4">Price</th>
              <th className="pb-4 text-center">Court booking</th>
              <th className="pb-4 text-center">Clinics</th>
              <th className="pb-4 text-center">Find a Buddy</th>
              <th className="pb-4 text-center">Lessons</th>
              <th className="pb-4 text-center">My Groups</th>
              <th className="pb-4 text-center">Sports Covered</th>
              <th className="pb-4 text-center">Advanced Booking</th>
            </tr>
          </thead>
          <tbody>
            {membershipPlans.length > 0 ? (
              membershipPlans?.map((plan) => (
                <tr key={plan.name} className="overflow-hidden">
                  <td className="rounded-l-xl bg-white px-4 py-3 text-gray-600">
                    {plan.plan_name}
                  </td>
                  <td className="bg-white px-4 py-3">
                    {fCurrency(plan.price)}
                  </td>
                  <td className="bg-white px-4 py-3">
                    <div className="flex justify-center">
                      <Switch
                        checked={plan.allow_court}
                        onClick={handleSwitchClick}
                        className={`${
                          plan.allow_court ? "bg-blue-500" : "bg-gray-200"
                        } relative inline-flex h-6 w-11 cursor-not-allowed items-center rounded-full transition-colors`}
                      >
                        <span
                          className={`${
                            plan.allow_court ? "translate-x-6" : "translate-x-1"
                          } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                        />
                      </Switch>
                    </div>
                  </td>
                  <td className="bg-white px-4 py-3">
                    <div className="flex justify-center">
                      <Switch
                        checked={plan.allow_clinic}
                        onClick={handleSwitchClick}
                        // onChange={() => handleToggle(plan.id, "allow_clinic")}
                        className={`${
                          plan.allow_clinic ? "bg-blue-500" : "bg-gray-200"
                        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                      >
                        <span
                          className={`${
                            plan.allow_clinic
                              ? "translate-x-6"
                              : "translate-x-1"
                          } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                        />
                      </Switch>
                    </div>
                  </td>
                  <td className="bg-white px-4 py-3">
                    <div className="flex justify-center">
                      <Switch
                        checked={plan.allow_buddy}
                        onClick={handleSwitchClick}
                        // onChange={() => handleToggle(plan.id, "allow_buddy")}
                        className={`${
                          plan.allow_buddy ? "bg-blue-500" : "bg-gray-200"
                        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                      >
                        <span
                          className={`${
                            plan.allow_buddy ? "translate-x-6" : "translate-x-1"
                          } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                        />
                      </Switch>
                    </div>
                  </td>
                  <td className="bg-white px-4 py-3">
                    <div className="flex justify-center">
                      <Switch
                        checked={plan.allow_coach}
                        onClick={handleSwitchClick}
                        // onChange={() => handleToggle(plan.id, "allow_coach")}
                        className={`${
                          plan.allow_coach ? "bg-blue-500" : "bg-gray-200"
                        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                      >
                        <span
                          className={`${
                            plan.allow_coach ? "translate-x-6" : "translate-x-1"
                          } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                        />
                      </Switch>
                    </div>
                  </td>
                  <td className="bg-white px-4 py-3">
                    <div className="flex items-center justify-center gap-2">
                      <Switch
                        checked={plan.allow_groups}
                        onClick={handleSwitchClick}
                        // onChange={() => handleToggle(plan.id, "allow_groups")}
                        className={`${
                          plan.allow_groups ? "bg-blue-500" : "bg-gray-200"
                        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors`}
                      >
                        <span
                          className={`${
                            plan.allow_groups
                              ? "translate-x-6"
                              : "translate-x-1"
                          } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                        />
                      </Switch>
                    </div>
                  </td>
                  <td className="bg-white px-4 py-3">
                    <div className="text-center">
                      <span className="text-xs text-gray-600">
                        {getSportNames(plan.applicable_sports)}
                      </span>
                    </div>
                  </td>
                  <td className="rounded-r-xl bg-white px-4 py-3">
                    <div className="flex items-center justify-between">
                      <div className="text-gray-500">
                        <span className="whitespace-nowrap">
                          {plan.advance_booking_enabled?.court === false
                            ? "Disabled"
                            : `${plan.advance_booking_days?.court || 10}d`}
                        </span>
                      </div>
                      <button
                        className="ml-5 flex items-center justify-center text-gray-500 transition-colors hover:text-gray-700"
                        onClick={() => {
                          setcurrentMode("edit");
                          handlePlanClick(plan);
                        }}
                      >
                        <FaArrowUpRightFromSquare className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="text-center text-sm text-gray-500">
                <td colSpan="8">No plans available</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <RightSideModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title="Plan details"
        onPrimaryAction={() => {
          setIsModalOpen(false);
        }}
        showFooter={false}
      >
        <PlanDetailsModal
          initialData={selectedPlan}
          mode={currentMode}
          // onUpdate={handlePlanUpdate}
          // onSubmit={(newPlan) => handlePlanCreate(newPlan, currentMode)}
          onSubmit={(newPlan) => handlePlanCreate(newPlan, currentMode)}
          onClose={handleCloseModal}
        />
      </RightSideModal>
    </div>
  );
}
