import React, { useState, useContext, useEffect } from "react";
import RightSideModal from "../RightSideModal";
import MkdSDK from "Utils/MkdSDK";
import { getManyByIds, GlobalContext } from "Context/Global";
import { AuthContext } from "Context/Auth";
import LoadingSpinner from "Components/LoadingSpinner";
import CourtReservationModal from "./CourtReservationModal";
import CoachReservationDetails from "./CoachReservationDetails";
import ClinicReservationDetails from "./ClinicReservationDetails";
import FindBudyRequestModal from "./FindBudyRequestModal";

let sdk = new MkdSDK();
export default function ReservationDetailModal({
  isOpen,
  onClose,
  clubSports,
  reservation,
  club,
  onReservationCanceled,
}) {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(false);
  const [players, setPlayers] = useState([]);
  const [coach, setCoach] = useState(null);

  async function getBuddyPlayers() {
    try {
      const players = await getManyByIds(
        globalDispatch,
        authDispatch,
        "user",
        JSON.parse(reservation?.player_ids),
        "user|user_id"
      );
      setPlayers(players.list);
    } catch (error) {
      console.error(error);
    }
  }

  async function getCoach() {
    try {
      sdk.setTable("user");
      const coachResponse = await sdk.callRestAPI(
        { id: reservation?.coach_id },
        "GET"
      );
      setCoach(coachResponse.model);
    } catch (error) {
      console.error(error);
    }
  }

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      await getBuddyPlayers();
      await getCoach();
      setIsLoading(false);
    })();
  }, [reservation]);

  if (!reservation) return null;
  if (isLoading) return <LoadingSpinner />;

  console.log({ reservation, sports: clubSports, players, coach });

  return (
    <RightSideModal
      isOpen={isOpen}
      onClose={onClose}
      title={
        reservation.booking_type === "Court"
          ? "Court details"
          : reservation.booking_type === "Coach"
          ? "Coach details"
          : reservation.booking_type === "Clinic"
          ? "Clinic details"
          : "Details"
      }
      showFooter={false}
      className={`!p-0`}
    >
      {reservation.booking_type === "Court" && (
        <CourtReservationModal
          reservation={reservation}
          club={club}
          clubSports={clubSports}
          players={players}
          onReservationCanceled={onReservationCanceled}
        />
      )}
      {reservation.booking_type === "Find Buddy" && (
        <FindBudyRequestModal
          reservation={reservation}
          club={club}
          clubSports={clubSports}
          players={players}
          onReservationCanceled={onReservationCanceled}
        />
      )}
      {reservation.booking_type === "Coach" && (
        <CoachReservationDetails
          reservation={reservation}
          club={club}
          clubSports={clubSports}
          players={players}
          coach={coach}
          onReservationCanceled={onReservationCanceled}
        />
      )}
      {reservation.booking_type === "Clinic" && (
        <ClinicReservationDetails
          reservation={reservation}
          club={club}
          clubSports={clubSports}
          players={players}
          onReservationCanceled={onReservationCanceled}
        />
      )}
    </RightSideModal>
  );
}
