import { fCurrency } from "Utils/formatNumber";
import moment from "moment";
import {
  convertTo12Hour,
  BOOKING_STATUSES,
  calculateReservationTimeLeft,
  canCancelReservation,
} from "Utils/utils";
import {
  ReservedStatus,
  PaidStatus,
  FailedStatus,
  TimeStatus,
  GroupFullStatus,
  LookingForBuddiesStatus,
} from "Components/ReservationStatus";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { useContext, useState } from "react";
import LoadingSpinner from "Components/LoadingSpinner";
import { CancelReservationModal } from "Components/UserFindBuddy/ReserveCourtModal";
const sdk = new MkdSDK();

// {
//   "buddy_id": 62,
//   "sport_id": 239,
//   "type": "Outdoor",
//   "sub_type": "Grass",
//   "sport_name": "Football",
//   "surface_id": 1,
//   "surface_name": "Hard Floor",
//   "num_players": 2,
//   "num_needed": 2,
//   "ntrp": 2.5,
//   "max_ntrp": 5.5,
//   "slots": "[{\"from\":\"10:00 AM\",\"until\":\"11:30 AM\"},{\"from\":\"12:30 PM\",\"until\":\"2:00 PM\"}]",
//   "need_coach": 1,
//   "notes": "Test",
//   "player_ids": "[200,201]",
//   "booking_date": "2025-04-23",
//   "start_time": "10:00:00",
//   "end_time": "14:00:00",
//   "reservation_created_at": "2025-04-09",
//   "reservation_updated_at": "2025-04-09T14:55:17.000Z",
//   "owner_first_name": "Ifeanyi",
//   "owner_last_name": "Lucky",
//   "owner_email": "<EMAIL>",
//   "owner_photo": "https://s3.us-east-2.amazonaws.com/com.mkdlabs.images/baas/courtmatchup/00867602096039992-632026e03c405_large.png",
//   "entry_type": "buddy",
//   "reserved": 0,
//   "player_details": [
//       {
//           "first_name": "Kabeer",
//           "last_name": "khan22",
//           "email": "<EMAIL>",
//           "ntrp": "7.5",
//           "photo": "https://s3.us-east-2.amazonaws.com/com.mkdlabs.images/baas/courtmatchup/0330543600721000171119.jpg"
//       }
//   ],
//   "reservation_status": 1,
//   "booking_type": "Find Buddy",
//   "custom_request": 1
// }

export default function FindBudyRequestModal({
  reservation,
  club,
  clubSports,
  players,
  onReservationCanceled,
}) {
  const navigate = useNavigate();
  const timeLeft = calculateReservationTimeLeft(
    reservation?.reservation_updated_at
  );
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [sendingEmail, setSendingEmail] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);

  const canCancel = canCancelReservation(reservation, clubSports);

  const handleSendEmailReminder = async (email) => {
    if (!email) {
      showToast(globalDispatch, "Email is required", 5000, "error");
      return;
    }

    try {
      setSendingEmail(true);
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/buddy/${reservation?.buddy_id}/send-mail?email=${email}`,
        {},
        "GET"
      );
      console.log(response);
      showToast(globalDispatch, "Email reminder sent", 5000, "success");
    } catch (error) {
      console.log(error);
      showToast(globalDispatch, error?.message, 5000, "error");
    } finally {
      setSendingEmail(false);
    }
  };

  const handleCancelReservation = async () => {
    setCancelLoading(true);
    try {
      // Use the reservation table to cancel the reservation
      sdk.setTable("reservation");
      await sdk.callRestAPI({ id: reservation?.reservation_id }, "DELETE");

      showToast(
        globalDispatch,
        "Reservation cancelled successfully",
        3000,
        "success"
      );

      setShowCancelModal(false);

      // Notify parent component to refresh data
      if (onReservationCanceled) {
        onReservationCanceled();
      }
    } catch (error) {
      console.error("Error cancelling reservation:", error);
      showToast(
        globalDispatch,
        error.message || "Error cancelling reservation",
        3000,
        "error"
      );
    } finally {
      setCancelLoading(false);
    }
  };
  return (
    <div>
      {sendingEmail && <LoadingSpinner />}
      <div className="space-y-6">
        <div className=" px-5 pt-5">
          <div className="flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3">
            <div>
              <div className="text-sm text-gray-500">STATUS</div>
            </div>
            <div className="flex items-center gap-2">
              <div>
                {reservation?.num_needed == reservation?.num_players && (
                  <GroupFullStatus title={"Group full"} />
                )}

                {reservation?.num_needed != reservation?.num_players && (
                  <LookingForBuddiesStatus
                    numberOfBuddies={reservation?.num_needed}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="divide-y px-5 py-1">
          <div className="py-3">
            <div className=" py-1 ">
              <p className="text-sm text-gray-500">REQUEST MADE</p>
            </div>
            {moment(reservation?.reservation_created_at).format("MMM D, YYYY")}
          </div>
          <div className="py-3">
            <div className=" py-1 ">
              <p className="text-sm text-gray-500">DTE & TIME</p>
            </div>
            {moment(reservation?.booking_date).format("MMM D, YYYY")} •{" "}
            {convertTo12Hour(reservation?.start_time)} -{" "}
            {convertTo12Hour(reservation?.end_time)}
          </div>
          <div className="py-3">
            <div className=" py-1 ">
              <p className="text-sm text-gray-500">SPORT</p>
            </div>
            {
              clubSports?.find((sport) => sport.id === reservation?.sport_id)
                ?.name
            }{" "}
            {reservation?.type && `• ${reservation?.type}`}{" "}
            {reservation?.sub_type && `• ${reservation?.sub_type}`}
          </div>
          <div className="py-3">
            <div className=" py-1 ">
              <p className="text-sm text-gray-500">NOTES</p>
            </div>
            <p className="mt-1 font-medium">
              {reservation?.ntrp}{" "}
              {reservation?.max_ntrp && `- ${reservation?.max_ntrp}`}
            </p>
          </div>
          <div className="py-3">
            <div className=" py-1 ">
              <p className="text-sm text-gray-500">LOOKING FOR PLAYERS</p>
            </div>
            <p className="mt-1 font-medium">
              {`${reservation?.num_needed}/${reservation?.num_players}`}
            </p>
          </div>

          <div className="py-3">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-500">PLAYERS</p>
              <button className="rounded-lg border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700">
                Email all
              </button>
            </div>
            <div className="mt-4 flex flex-col gap-4">
              {players.map((player) => (
                <div
                  key={player.user_id}
                  className="flex items-center justify-between rounded-lg bg-gray-100 p-2"
                >
                  <div className="flex items-center gap-3">
                    <img
                      src={player.photo || "/default-avatar.png"}
                      alt={player.first_name}
                      className="h-10 w-10 rounded-full object-cover"
                    />
                    <div>
                      <p className="font-medium capitalize">
                        {player.first_name || player.last_name
                          ? `${player.first_name} ${player.last_name}`
                          : "Player"}
                      </p>
                      <p className="text-sm text-gray-500">{player.email}</p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-3">
                    <button
                      onClick={() => handleSendEmailReminder(player.email)}
                      className="rounded-lg bg-white px-2 py-1 text-sm text-gray-700"
                    >
                      Send email
                    </button>
                    <p className="text-sm text-gray-700">NTRP: {player.ntrp}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="px-5 py-3">
          <div className="rounded-xl bg-gray-100 p-3">
            <p className="text-sm text-gray-500">FEES</p>
            <div className="mt-2">
              <div className="mb-2 flex justify-between">
                <p className="text-sm ">Club fee</p>
                <p className="text-sm ">{fCurrency(reservation?.club_fee)}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm ">Service fee</p>
                <p className="text-sm ">
                  {fCurrency(reservation?.service_fee)}
                </p>
              </div>
              <div className="my-2 border-t border-gray-300"></div>
              <div className="flex justify-between font-medium">
                <p>Total</p>
                <p>
                  {fCurrency(reservation?.club_fee + reservation?.service_fee)}
                </p>
              </div>
            </div>
            {reservation?.booking_status == BOOKING_STATUSES.PENDING && (
              <div className="mt-3 flex items-center justify-between">
                <button
                  onClick={() => {
                    navigate(
                      `/user/reservation-payment/${reservation?.reservation_id}?type=${reservation?.booking_type}`
                    );
                  }}
                  disabled={timeLeft === "0min"}
                  className={`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${
                    timeLeft === "0min"
                      ? "cursor-not-allowed bg-gray-400"
                      : "bg-primaryBlue"
                  }`}
                >
                  {timeLeft === "0min" ? "Time Expired" : "Pay now"}
                </button>
              </div>
            )}

            {reservation?.booking_status == BOOKING_STATUSES.SUCCESS && (
              <div className="mt-3 flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75"
                      stroke="#868C98"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>

                  <p className="text-sm font-medium">Credit card • 0089</p>
                </div>
                <button
                  onClick={() => {
                    navigate(
                      `/user/payment-receipt/${reservation?.reservation_id}`
                    );
                  }}
                  className="rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500"
                >
                  View receipt
                </button>
              </div>
            )}

            {/* Cancel button - only show if cancellation is allowed */}
            {canCancel &&
              reservation?.booking_status === BOOKING_STATUSES.SUCCESS && (
                <div className="mt-3">
                  <button
                    onClick={() => setShowCancelModal(true)}
                    className="w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100"
                  >
                    Cancel Reservation
                  </button>
                </div>
              )}

            <div className="mt-3">
              <p className="text-xs text-gray-500">
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae
                id illum labore excepturi veritatis facere ducimus architecto
                quasi incidunt earum eligendi exercitationem, ipsam voluptas
                amet quaerat? Alias inventore eum debitis!
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Cancel Confirmation Modal */}
      <CancelReservationModal
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        onDelete={handleCancelReservation}
        loading={cancelLoading}
      />
    </div>
  );
}
