import React, { useState, useContext, useMemo, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { <PERSON><PERSON>ye, FiEyeOff } from "react-icons/fi";
import MkdSDK from "Utils/MkdSDK";
import { showToast, GlobalContext } from "Context/Global";
import { InteractiveButton } from "Components/InteractiveButton";
import PhoneInput from "react-phone-input-2";
import AutocompleteInput from "./AutocompleteInput";
import { Country, State, City } from "country-state-city";
import "Src/styles/PhoneInput.css";
import "react-phone-input-2/lib/style.css";

let sdk = new MkdSDK();

const schema = yup.object().shape({
  firstName: yup.string().required("First name is required"),
  lastName: yup.string().required("Last name is required"),
  gender: yup.string(),
  familyRole: yup.string().required("Family role is required"),
  email: yup.string().email("Invalid email format"),
  allowLogin: yup.boolean(),

  password: yup.string().when("allowLogin", {
    is: true,
    then: () =>
      yup
        .string()
        .required("Password is required")
        .min(6, "Password must be at least 6 characters"),
    otherwise: () => yup.string(),
  }),
  phoneNumber: yup.string().required("Phone number is required"),
  dateOfBirth: yup.string().required("Date of birth is required"),
  address: yup.string().required("Address is required"),
  country: yup.string().required("Country is required"),
  city: yup.string().required("City is required"),
  state: yup.string().required("State is required"),
  zipCode: yup.string().required("Zip code is required"),
  alternatePhone: yup.string(),
  ageGroup: yup.string().required("Age group is required"),
  additionalInfo: yup.string(),
});

export default function AddFamilyMemberForm({
  onSubmit,
  onClose,
  user,
  group,
  fetchData,
}) {
  const [showPassword, setShowPassword] = useState(false);
  const [allowLogin, setAllowLogin] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: "",
      lastName: "",
      gender: "",
      familyRole: "",
      email: "",
      username: "",
      password: "",
      phoneNumber: "",
      dateOfBirth: "",
      address: "",
      country: "",
      city: "",
      state: "",
      zipCode: "",
      alternatePhone: "",
      ageGroup: "",
      additionalInfo: "",
      allowLogin: false,
    },
  });

  const selectedCountry = watch("country");
  const selectedState = watch("state");

  // Get all countries
  const countries = useMemo(
    () =>
      Country.getAllCountries().map((country) => ({
        value: country.isoCode,
        label: country.name,
      })),
    []
  );

  // Update states when country changes
  useEffect(() => {
    if (selectedCountry) {
      const countryStates = State.getStatesOfCountry(selectedCountry).map(
        (state) => ({
          value: state.isoCode,
          label: state.name,
        })
      );
      setStates(countryStates);
      setValue("state", ""); // Reset state when country changes
      setValue("city", ""); // Reset city when country changes
    }
  }, [selectedCountry, setValue]);

  // Update cities when state changes
  useEffect(() => {
    if (selectedCountry && selectedState) {
      const stateCities = City.getCitiesOfState(
        selectedCountry,
        selectedState
      ).map((city) => ({
        value: city.name,
        label: city.name,
      }));
      setCities(stateCities);
    }
  }, [selectedCountry, selectedState]);
  //   console.log("group", group);
  console.log("errors", errors);
  const onFormSubmit = async (data) => {
    setSubmitting(true);
    console.log(data);

    const payload = {
      email: data.email || "",
      password: data.password || "",
      role: "user",
      verify: true,
      is_refresh: false,
      first_name: data.firstName,
      last_name: data.lastName,
      photo: "",
      password_login: data.allowLogin ? 1 : 0,
      phone: data.phoneNumber,
      alternative_phone: data.alternatePhone,
      age_group: data.ageGroup,
      another: "",
      family_role: data.familyRole,
      address: data.address,
      city: data.city,
      state: data.state,
      country: data.country,
      zip_code: data.zipCode,
      house_no: "",
      date_of_birth: data.dateOfBirth,
      club_id: user.club_id,
      guardian: user.id,
    };
    // console.log("payload", payload);

    try {
      const createFamilyMemberResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/users/register`,
        payload,
        "POST"
      );
      console.log(createFamilyMemberResponse);
      const addMemberResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/groups/add/${group.group_id}`,
        {
          members: [createFamilyMemberResponse.user_id],
        },
        "POST"
      );
      showToast(
        globalDispatch,
        "Family member created successfully",
        3000,
        "success"
      );
      await fetchData();
      onClose();
    } catch (error) {
      showToast(globalDispatch, error?.message, 3000, "error");
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)}>
      <div className="space-y-4 py-5">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            First name
          </label>
          <input
            type="text"
            {...register("firstName")}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          />
          {errors.firstName && (
            <p className="mt-1 text-sm text-red-600">
              {errors.firstName.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Last name
          </label>
          <input
            type="text"
            {...register("lastName")}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          />
          {errors.lastName && (
            <p className="mt-1 text-sm text-red-600">
              {errors.lastName.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Gender <span className="text-gray-500">(Optional)</span>
          </label>
          <select
            {...register("gender")}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          >
            <option value="">- select -</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Family role
          </label>
          <select
            {...register("familyRole")}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          >
            <option value="">- select -</option>
            <option value="Parent">Parent</option>
            <option value="Child">Child</option>
            <option value="Sibling">Sibling</option>
            <option value="Grandparent">Grandparent</option>
            <option value="Spouse">Spouse</option>
            <option value="Aunt">Aunt</option>
            <option value="Uncle">Uncle</option>
            <option value="Cousin">Cousin</option>
            <option value="Guardian">Guardian</option>
            <option value="Step-parent">Step-parent</option>
            <option value="Step-sibling">Step-sibling</option>
            <option value="In-law">In-law</option>
            <option value="Godparent">Godparent</option>
          </select>
          {errors.familyRole && (
            <p className="mt-1 text-sm text-red-600">
              {errors.familyRole.message}
            </p>
          )}
        </div>

        {!allowLogin && (
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Email <span className="text-gray-500">(Optional)</span>
            </label>
            <input
              type="email"
              {...register("email")}
              className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>
        )}

        <div className="space-y-4 rounded-xl bg-gray-50 p-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="allowLogin"
              {...register("allowLogin")}
              onChange={(e) => {
                setValue("allowLogin", e.target.checked);
                setAllowLogin(e.target.checked);
              }}
              className="h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"
            />
            <label
              htmlFor="allowLogin"
              className="text-sm font-medium text-gray-700"
            >
              Allow log in
            </label>
          </div>

          {allowLogin && (
            <>
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700">
                  Email
                </label>
                <input
                  type="email"
                  {...register("email")}
                  className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div className="relative">
                <label className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="relative mt-1">
                  <input
                    type={showPassword ? "text" : "password"}
                    {...register("password")}
                    className="block w-full rounded-xl border border-gray-300 px-3 py-2 pr-10 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 flex items-center pr-3"
                  >
                    {showPassword ? (
                      <FiEyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <FiEye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.password.message}
                  </p>
                )}
              </div>
            </>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Phone number
          </label>
          <div className="mt-1 flex">
            <Controller
              name="phoneNumber"
              control={control}
              render={({ field: { onChange, value } }) => (
                <PhoneInput
                  country={"us"}
                  value={value}
                  onChange={onChange}
                  containerClass="mt-1"
                  inputClass="!w-full !h-[42px] !rounded-xl !border-zinc-200"
                  buttonClass="!border-zinc-200 !rounded-l-xl"
                  placeholder="(*************"
                />
              )}
            />
          </div>
          {errors.phoneNumber && (
            <p className="mt-1 text-sm text-red-600">
              {errors.phoneNumber.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Date of birth
          </label>
          <input
            type="date"
            {...register("dateOfBirth")}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          />
          {errors.dateOfBirth && (
            <p className="mt-1 text-sm text-red-600">
              {errors.dateOfBirth.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Address
          </label>
          <input
            type="text"
            {...register("address")}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          />
          {errors.address && (
            <p className="mt-1 text-sm text-red-600">
              {errors.address.message}
            </p>
          )}
        </div>

        <div>
          <Controller
            name="country"
            control={control}
            render={({ field }) => (
              <AutocompleteInput
                label="Country"
                options={countries}
                value={field.value}
                onChange={(option) => field.onChange(option.value)}
                placeholder="Search country..."
              />
            )}
          />
          {errors.country && (
            <p className="mt-1 text-sm text-red-600">
              {errors.country.message}
            </p>
          )}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Controller
              name="state"
              control={control}
              render={({ field }) => (
                <AutocompleteInput
                  label="State"
                  options={states}
                  value={field.value}
                  onChange={(option) => field.onChange(option.value)}
                  placeholder="Search state..."
                  disabled={!selectedCountry}
                />
              )}
            />
            {errors.state && (
              <p className="mt-1 text-sm text-red-600">
                {errors.state.message}
              </p>
            )}
          </div>

          <div>
            <Controller
              name="city"
              control={control}
              render={({ field }) => (
                <AutocompleteInput
                  label="City"
                  options={cities}
                  value={field.value}
                  onChange={(option) => field.onChange(option.value)}
                  placeholder="Search city..."
                  disabled={!selectedState}
                />
              )}
            />
            {errors.city && (
              <p className="mt-1 text-sm text-red-600">{errors.city.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Zip code
          </label>
          <input
            type="text"
            {...register("zipCode")}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          />
          {errors.zipCode && (
            <p className="mt-1 text-sm text-red-600">
              {errors.zipCode.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Alternate phone number{" "}
            <span className="text-gray-500">(Optional)</span>
          </label>
          <div className="mt-1 flex">
            <Controller
              name="alternatePhone"
              control={control}
              render={({ field: { onChange, value } }) => (
                <PhoneInput
                  country={"us"}
                  value={value}
                  onChange={onChange}
                  containerClass="mt-1"
                  inputClass="!w-full !h-[42px] !rounded-xl !border-zinc-200"
                  buttonClass="!border-zinc-200 !rounded-l-xl"
                  placeholder="(*************"
                />
              )}
            />
          </div>
          {errors.alternatePhone && (
            <p className="mt-1 text-sm text-red-600">
              {errors.alternatePhone.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Age group
          </label>
          <select
            {...register("ageGroup")}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          >
            <option value="">- select -</option>
            <option value="toddler">3-5 years (Toddler)</option>
            <option value="child">6-12 years (Child)</option>
            <option value="teen">13-17 years (Teen)</option>
            <option value="young_adult">18-25 years (Young Adult)</option>
            <option value="adult">26-39 years (Adult)</option>
            <option value="middle_aged">40-59 years (Middle-aged)</option>
            <option value="senior">60+ years (Senior)</option>
          </select>
          {errors.ageGroup && (
            <p className="mt-1 text-sm text-red-600">
              {errors.ageGroup.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Additional info <span className="text-gray-500">(Optional)</span>
          </label>
          <textarea
            {...register("additionalInfo")}
            rows={3}
            className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"
          />
        </div>
      </div>

      <div className="sticky bottom-0 mt-auto flex justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4">
        <button
          type="button"
          className="flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50"
          onClick={onClose}
        >
          Cancel
        </button>
        <InteractiveButton
          type="submit"
          className="flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700"
          loading={submitting}
        >
          {submitting ? "Saving..." : "Add family member"}
        </InteractiveButton>
      </div>
    </form>
  );
}
